export interface EvaluationActivityScore {
  activity_id: number;
  player_id: number;
  player_type: number;
  assign_to_id: number;
  score: number;
  activity_setting_score: number;
  score_type: number;
}

export interface EvaluationPlayerScore {
  activity_id: number;
  player_id: number;
  player_type: number;
  score_type: number;
  be_score_user_ids: string[];
  assign_to_id: number;
  final_score: number;
  activity_setting_score: number;
}

export interface EvaluationScoreWithMembers extends EvaluationActivityScore {
  be_score_user_ids: string[];
}

export interface SubgroupMember {
  user_id: string;
  [key: string]: any;
}

export interface EvaluationActivityJobData {
  data: {
    group_id: number;
    activity_ids: number[];
    subgroups: Record<string, SubgroupMember[]>;
  };
}
