import { Entity, Column } from 'typeorm';
import { CustomBaseEntity } from './base.entity';

@Entity('grade_scoring_items')
export class GradeScoringItemEntity extends CustomBaseEntity {
  @Column({ type: 'bigint', name: 'group_id', nullable: true })
  groupId: number;

  @Column({ type: 'integer', name: 'scoring_item_type', nullable: true })
  scoringItemType: number;

  @Column({ type: 'real', nullable: true })
  score: number;

  @Column({ type: 'real', nullable: true })
  percent: number;

  @Column({ type: 'json', name: 'scoring_item', nullable: true, default: '{}' })
  scoringItem: object;

  @Column({ type: 'bigint' })
  creator: number;

  @Column({
    type: 'boolean',
    name: 'is_selected',
    nullable: true,
    default: false,
  })
  isSelected: boolean;

  @Column({ type: 'varchar', nullable: true, default: '' })
  title: string;

  @Column({ type: 'integer', name: 'setting_type', nullable: true, default: 1 })
  settingType: number;
}
