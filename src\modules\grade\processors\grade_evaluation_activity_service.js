import { utils } from 'aia-base';
import BigNumber from 'bignumber.js';
import _ from 'lodash';
import constants from '../../common/constants';
import EvaluationActivityModule from '../../modules/overall_evaluation/evaluation_activity_module';

const evaluationActivityModule = new EvaluationActivityModule();

class GradeEvaluationActivityService {
  /**
   * 获取学生评价活动得分
   * @param {*} user_id
   * @param {*} evaluation_activity_setting_score 评价活动评分项设置的分数
   * @param {*} evaluation_activity_stat_type
   * @param {*} evaluation_activity_scores
   * @returns
   */
  getEvaluationActivityGradeWithUser(user_id, evaluation_activity_setting_score, evaluation_activity_stat_type, evaluation_activity_scores) {
    // 累计总评价分
    let totalSettingScore = 0;
    //  累计总评价得分
    let totalGetScore = 0;
    //  被评价得分的次数
    let totalEvaluationNumber = 0;
    // 最高得分比
    let maxGradeRate = 0;
    // 累计的得分/试卷分数比
    let sumGradeRate = 0;

    _.forEach(evaluation_activity_scores, (evaluation_score) => {
      const { be_score_user_ids, final_score, activity_setting_score } = evaluation_score;

      // 评价活动和当前学生相关，且产生评价结果
      if (_.indexOf(be_score_user_ids, user_id) > -1) {
        totalSettingScore += parseFloat(activity_setting_score);
        totalGetScore = new BigNumber(totalGetScore).plus(final_score).toNumber().toFixed(6);

        totalEvaluationNumber++;
        const scoreRate = utils.ratioCalculation(final_score, activity_setting_score, 6);
        if (scoreRate > maxGradeRate) {
          maxGradeRate = scoreRate;
        }

        sumGradeRate = new BigNumber(sumGradeRate).plus(scoreRate).toNumber().toFixed(6);
      }
    });

    // 相加分
    if (evaluation_activity_stat_type === constants.grade_manager.paper_calc_score_type.sum) {
      return {
        evaluation_activity_score: utils.multiplyCalculation(utils.ratioCalculation(totalGetScore, totalSettingScore, 6), evaluation_activity_setting_score, 2),
        totalEvaluationNumber,
      };
    }

    // 平均分
    if (evaluation_activity_stat_type === constants.grade_manager.paper_calc_score_type.avg) {
      return {
        evaluation_activity_score: utils.multiplyCalculation(
          utils.ratioCalculation(sumGradeRate, totalEvaluationNumber, 6),
          evaluation_activity_setting_score,
          2
        ),
        totalEvaluationNumber,
      };
    }

    // 最高分
    if (evaluation_activity_stat_type === constants.grade_manager.paper_calc_score_type.max) {
      return { evaluation_activity_score: utils.multiplyCalculation(maxGradeRate, evaluation_activity_setting_score, 2), totalEvaluationNumber };
    }
  }

  /**
   * 获取被评对象的得分
   * @param {*} group_id
   * @param {*} subgroups
   * @returns
   */
  async getGroupEvaluationPlayerScore(group_id, subgroups, activity_ids) {
    if (!_.size(activity_ids)) return [];

    const activityPlayerScore = await evaluationActivityModule.queryGroupEvaluationScore(group_id, activity_ids).then((data) => {
      return _.map(data, (player) => {
        return {
          activity_id: player.activity_id,
          player_id: player.player_id,
          player_type: player.player_type,
          assign_to_id: player.assign_to_id,
          score: isNaN(player.score) ? 0 : player.score,
          activity_setting_score: player.total_score,
          score_type: player.score_type,
        };
      });
    });

    // 计分同一个被评对象,有效的得分成员
    const scoreMembers = _.map(activityPlayerScore, (player) => {
      const { assign_to_id, player_type } = player;
      // user_id', 'subgroup_groups_id
      if (player_type === constants.overall_evaluation.player_type.class || player_type === constants.overall_evaluation.player_type.subgroup) {
        return {
          ...player,
          be_score_user_ids: _.keys(_.groupBy(subgroups[assign_to_id], 'user_id')), // 得分成员
        };
      } else {
        return {
          ...player,
          be_score_user_ids: [assign_to_id],
        };
      }
    });

    // 根据计分项, 计算被评对象
    return _.map(_.groupBy(scoreMembers, 'player_id'), (player_scores) => {
      const { activity_id, player_id, player_type, score_type, be_score_user_ids, assign_to_id, activity_setting_score } = _.head(player_scores);

      // 被评得分次数
      let beScoreCount = 0;
      let totalScore = 0;

      // 评价活动最高分模式下，如果打分的人不满三个人，则走平均分模式
      if (score_type === constants.overall_evaluation.score_type.avg_exclude_min_max && _.size(player_scores) >= 3) {
        const scoreWithOutMaxAndMin = this.pullMaxAndMixScore(player_scores);
        beScoreCount = _.size(scoreWithOutMaxAndMin);
        totalScore = _.sumBy(scoreWithOutMaxAndMin, 'score');
      } else {
        beScoreCount = _.size(player_scores);
        totalScore = _.sumBy(player_scores, 'score');
      }

      let finalScore = 0;
      if (beScoreCount > 0) {
        finalScore = utils.ratioCalculation(totalScore, beScoreCount, 2);
      }

      return {
        activity_id,
        player_id,
        player_type,
        score_type,
        be_score_user_ids,
        assign_to_id,
        final_score: finalScore,
        activity_setting_score,
      };
    });
  }

  /**
   * 最高分和最低分去除后的评分
   * @param {*} items
   * @returns
   */
  pullMaxAndMixScore(items) {
    // 找到最小和最大分数的对象
    const minItem = _.minBy(items, 'score');
    const maxItem = _.maxBy(items, 'score');

    // 从原始数组中删除这些对象
    return _.pull(items, minItem, maxItem);
  }
}

export default GradeEvaluationActivityService;
