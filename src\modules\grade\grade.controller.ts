import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  ParseIntPipe,
} from '@nestjs/common';
import { GradeService } from './grade.service';
import { CreateGradeDto } from './dto/create-grade.dto';
import { UpdateGradeDto } from './dto/update-grade.dto';
import { GradeFlowService } from './services/gradeFlowService';
import { COURSE_GRADE_OPS } from './enums/course-grade-ops.enum';

@Controller('grade')
export class GradeController {
  constructor(
    private readonly gradeService: GradeService,
    private readonly gradeFlowService: GradeFlowService,
  ) {}

  @Post()
  create(@Body() createGradeDto: CreateGradeDto) {
    return this.gradeService.create(createGradeDto);
  }

  @Get()
  findAll() {
    return this.gradeService.findAll();
  }

  @Get(':id')
  findOne(@Param('id', ParseIntPipe) id: number) {
    return this.gradeService.findOne(id);
  }

  @Patch(':id')
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateGradeDto: UpdateGradeDto,
  ) {
    return this.gradeService.update(id, updateGradeDto);
  }

  @Delete(':id')
  remove(@Param('id', ParseIntPipe) id: number) {
    return this.gradeService.remove(id);
  }

  @Post('score')
  async getCourseGrade(@Body() body: any) {
    return await this.gradeFlowService.createFlow(body, COURSE_GRADE_OPS.SCORE);
  }
}
