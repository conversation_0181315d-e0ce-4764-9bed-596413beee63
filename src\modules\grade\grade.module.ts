import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { QueueModule } from '../queue-board/queue-board.module';
import { GradeService } from './grade.service';
import { GradeController } from './grade.controller';
import { ActivityNodeRepository } from '@app/shared/database/repositories/activity-node.repository';
import { ActivityNodeEntity } from '@app/shared/database/entities/activity-node.entity';
import { EvaluationActivityEntity } from '@app/shared/database/entities/evaluation-activity.entity';
import { EvaluationActivityRepository } from '@app/shared/database/repositories/evaluation-activity.repository';
import { MemberEntity } from '@app/shared/database/entities/member.entity';
import { MemberRepository } from '@app/shared/database/repositories/member.repository';
import { MEMBER_REPOSITORY } from '@app/shared/database/interfaces/member.repository.interface';
import { GradeFlowService } from './services/gradeFlowService';
import { CourseGradeMergeProcessor } from './processors/course-grade-merge.processor';
import { CourseGradeChildProcessor } from './processors/course-grade-child.processor';
import { EvaluationActivityService } from './services/evaluation-activity.service';
import { EVALUATION_ACTIVITY_REPOSITORY } from '@app/shared/database/interfaces/evaluation-activity.repository.interface';
import {
  COURSE_GRADE_CHILD,
  COURSE_GRADE_MERGE,
  COURSE_GRADE_PRODUCER,
} from './constants/grade.constant';

@Module({
  imports: [
    QueueModule.register({
      queues: [COURSE_GRADE_MERGE, COURSE_GRADE_CHILD],
      flows: [COURSE_GRADE_PRODUCER],
    }),
    TypeOrmModule.forFeature([
      ActivityNodeEntity,
      EvaluationActivityEntity,
      MemberEntity,
    ]),
  ],
  controllers: [GradeController],
  providers: [
    GradeService,
    GradeFlowService,
    EvaluationActivityService,
    {
      provide: 'ActivityNodeRepositoryInterface',
      useClass: ActivityNodeRepository,
    },
    {
      provide: EVALUATION_ACTIVITY_REPOSITORY,
      useClass: EvaluationActivityRepository,
    },
    {
      provide: MEMBER_REPOSITORY,
      useClass: MemberRepository,
    },
    CourseGradeMergeProcessor,
    CourseGradeChildProcessor,
  ],
})
export class GradeModule {}
