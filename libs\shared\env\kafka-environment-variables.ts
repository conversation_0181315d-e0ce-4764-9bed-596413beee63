import { Expose, Type } from 'class-transformer';
import { isString, IsString, IsUrl } from 'class-validator';
import { decorate } from 'ts-mixer';

export class KafkaEnvironmentVariables {
  @decorate(Expose())
  @decorate(IsString())
  KAFKA_URI: string;

  @decorate(Expose())
  @decorate(IsString())
  KAFKA_RAW_MESSAGE_TOPIC: string;

  @decorate(Expose())
  @decorate(IsString())
  KAFKA_RAW_MESSAGE_GROUP: string;

  @decorate(Expose())
  @decorate(IsString())
  KAFKA_READY_MESSAGE_TOPIC: string;

  @decorate(Expose())
  @decorate(IsString())
  KAFKA_ANALYSIS_MESSAGE_TOPIC: string;

  @decorate(Expose())
  @decorate(IsString())
  KAFKA_ANALYSIS_MESSAGE_GROUP: string;
}
