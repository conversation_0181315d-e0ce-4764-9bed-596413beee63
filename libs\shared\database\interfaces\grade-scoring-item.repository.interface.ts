import { BaseInterfaceRepository } from '../repositories/base/base.interface.repository';
import { GradeScoringItemEntity } from '../entities/grade-scoring-item.entity';

export interface GradeScoringItemRepositoryInterface
  extends BaseInterfaceRepository<GradeScoringItemEntity> {
  findByGroupId(groupId: number): Promise<GradeScoringItemEntity[]>;
  findByCreator(creator: number): Promise<GradeScoringItemEntity[]>;
  findByGroupIdAndType(
    groupId: number,
    scoringItemType: number,
  ): Promise<GradeScoringItemEntity[]>;
  findSelectedItems(groupId: number): Promise<GradeScoringItemEntity[]>;
  findBySettingType(settingType: number): Promise<GradeScoringItemEntity[]>;
  findByGroupIdAndSettingType(
    groupId: number,
    settingType: number,
  ): Promise<GradeScoringItemEntity[]>;
}

export const GRADE_SCORING_ITEM_REPOSITORY =
  'GradeScoringItemRepositoryInterface';
