import { Entity, Column } from 'typeorm';
import { CustomBaseEntity } from './base.entity';

@Entity('evaluation_activities')
export class EvaluationActivityEntity extends CustomBaseEntity {
  @Column({ type: 'bigint' })
  groupId: number;

  @Column({ type: 'integer' })
  playerType: number;

  @Column({ type: 'integer' })
  scoreType: number;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  totalScore: number;

  @Column({ type: 'integer', default: 1 })
  status: number;
}
