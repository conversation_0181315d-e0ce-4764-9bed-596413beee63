const _ = require('lodash');

const playerScores = [
  {
    activity_id: '6790882312910313319',
    player_id: '6790882313103251306',
    player_type: 2,
    score_type: 1,
    be_score_user_ids: ['5712419859057345940'],
    assign_to_id: '5712419859057345940',
    final_score: 50,
    activity_setting_score: 100,
  },
  {
    activity_id: '6790882312910313319',
    player_id: '6790882313170360171',
    player_type: 2,
    score_type: 1,
    be_score_user_ids: ['5711770941852222696'],
    assign_to_id: '5711770941852222696',
    final_score: 100,
    activity_setting_score: 100,
  },
  {
    activity_id: '6790883042643683142',
    player_id: '6790883042752735049',
    player_type: 2,
    score_type: 1,
    be_score_user_ids: ['5712419859057345940'],
    assign_to_id: '5712419859057345940',
    final_score: 50,
    activity_setting_score: 100,
  },
  {
    activity_id: '6790883042643683142',
    player_id: '6790883042761123658',
    player_type: 2,
    score_type: 1,
    be_score_user_ids: ['5711770941852222696'],
    assign_to_id: '5711770941852222696',
    final_score: 100,
    activity_setting_score: 100,
  },
];

const result = _.chain(playerScores)
  .flatMap((score) => {
    const { be_score_user_ids, ...scoreData } = score;
    return be_score_user_ids.map((userId) => ({
      userId,
      scoreData,
    }));
  })
  .groupBy('userId')
  .mapValues((items) => _.map(items, 'scoreData'))
  .value();
console.log(result);
