import {
  ArrayNotEmpty,
  IsArray,
  IsNotEmpty,
  IsString,
  IsNumber,
  IsOptional,
  IsObject,
} from 'class-validator';
import { SubgroupMember } from '../interfaces/evaluation-activity.interface';

export class CourseGradeDto {
  @IsNotEmpty()
  @IsString()
  group_id: string;

  @IsArray()
  @ArrayNotEmpty()
  @IsNumber({}, { each: true })
  scoringItemTypes: number[];

  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  activity_ids?: number[];

  @IsOptional()
  @IsObject()
  subgroups?: Record<string, SubgroupMember[]>;
}
