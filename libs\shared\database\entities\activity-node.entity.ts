import { Entity, Column } from 'typeorm';
import { CustomBaseEntity } from './base.entity';

@Entity('activity_nodes')
export class ActivityNodeEntity extends CustomBaseEntity {
  @Column({ type: 'bigint', nullable: true })
  parentId: number;

  @Column({ type: 'text', nullable: true })
  path: string;

  @Column({ type: 'varchar', nullable: true })
  level: string;

  @Column({ type: 'varchar', nullable: true })
  name: string;

  @Column({ type: 'integer', nullable: true })
  type: number;

  @Column({ type: 'varchar', nullable: true })
  mimetype: string;

  @Column({ type: 'bigint', nullable: true })
  creator: number;

  @Column({ type: 'bigint' })
  groupId: number;

  @Column({ type: 'bigint', nullable: true })
  quoteId: number;

  @Column({ type: 'integer', nullable: true })
  del: number;

  @Column({ type: 'integer', nullable: true })
  public: number;

  @Column({ type: 'integer', nullable: true })
  lock: number;

  @Column({ type: 'integer', nullable: true })
  download: number;

  @Column({ type: 'integer', nullable: true })
  copy: number;

  @Column({ type: 'json', nullable: true })
  property: any;

  @Column({ type: 'integer' })
  sortPosition: number;

  @Column({ type: 'integer', nullable: true })
  finishTeaching: number;

  @Column({ type: 'integer', nullable: true })
  published: number;

  @Column({ type: 'bigint', nullable: true })
  publishRecordId: number;

  @Column({ type: 'text', nullable: true })
  tag: string;

  @Column({ type: 'integer', nullable: true })
  resourceType: number;

  @Column({ type: 'bigint', nullable: true })
  author: number;
}
