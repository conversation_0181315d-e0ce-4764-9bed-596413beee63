import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migration1750631234408 implements MigrationInterface {
  name = 'Migration1750631234408';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE "authors" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                "deleted_at" TIMESTAMP,
                "deleted_by" uuid,
                "updated_by" uuid,
                "created_by" uuid,
                "name" character varying NOT NULL,
                "biography" character varying,
                "birth_date" date,
                "death_date" date,
                "nationality" character varying,
                "website" character varying,
                "email" character varying,
                "status" character varying NOT NULL DEFAULT 'active',
                CONSTRAINT "UQ_d2ed02fabd9b52847ccb85e6b88" UNIQUE ("id"),
                CONSTRAINT "PK_d2ed02fabd9b52847ccb85e6b88" PRIMARY KEY ("id")
            )
        `);
    await queryRunner.query(`
            CREATE TABLE "books" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                "deleted_at" TIMESTAMP,
                "deleted_by" uuid,
                "updated_by" uuid,
                "created_by" uuid,
                "title" character varying NOT NULL,
                "isbn" character varying NOT NULL,
                "price" numeric(10, 2) NOT NULL,
                "price_currency" character varying(3) NOT NULL,
                "description" text,
                "publication_date" date,
                "genre" character varying,
                "stock_quantity" integer NOT NULL DEFAULT '0',
                "cover_image_url" character varying,
                "status" character varying NOT NULL,
                "author_id" uuid NOT NULL,
                CONSTRAINT "UQ_f3f2f25a099d24e12545b70b022" UNIQUE ("id"),
                CONSTRAINT "UQ_54337dc30d9bb2c3fadebc69094" UNIQUE ("isbn"),
                CONSTRAINT "PK_f3f2f25a099d24e12545b70b022" PRIMARY KEY ("id")
            )
        `);
    await queryRunner.query(`
            CREATE TABLE "borrowings" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                "deleted_at" TIMESTAMP,
                "deleted_by" uuid,
                "updated_by" uuid,
                "created_by" uuid,
                "customer_email" character varying NOT NULL,
                "customer_phone" character varying NOT NULL,
                "customer_name" character varying NOT NULL,
                "borrow_date" date NOT NULL,
                "due_date" date NOT NULL,
                "return_date" date,
                "status" character varying NOT NULL DEFAULT 'borrowed',
                "fine_amount" numeric(10, 2) NOT NULL DEFAULT '0',
                "notes" text,
                "book_id" uuid NOT NULL,
                "delivery_address" character varying NOT NULL,
                CONSTRAINT "UQ_5da0d5a9a91e8c386e1f6812db2" UNIQUE ("id"),
                CONSTRAINT "PK_5da0d5a9a91e8c386e1f6812db2" PRIMARY KEY ("id")
            )
        `);
    await queryRunner.query(`
            ALTER TABLE "books"
            ADD CONSTRAINT "FK_1056dbee4616479f7d562c562df" FOREIGN KEY ("author_id") REFERENCES "authors"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE "borrowings"
            ADD CONSTRAINT "FK_b65e33ab3f4fd885a212491011d" FOREIGN KEY ("book_id") REFERENCES "books"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "borrowings" DROP CONSTRAINT "FK_b65e33ab3f4fd885a212491011d"
        `);
    await queryRunner.query(`
            ALTER TABLE "books" DROP CONSTRAINT "FK_1056dbee4616479f7d562c562df"
        `);
    await queryRunner.query(`
            DROP TABLE "borrowings"
        `);
    await queryRunner.query(`
            DROP TABLE "books"
        `);
    await queryRunner.query(`
            DROP TABLE "authors"
        `);
  }
}
