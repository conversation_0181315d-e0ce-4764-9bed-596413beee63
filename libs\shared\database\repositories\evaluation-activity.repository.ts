import { Injectable } from '@nestjs/common';
import { InjectRepository, InjectDataSource } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { BaseAbstractRepository } from './base/base.abstract.repository';
import { EvaluationActivityScore } from '../../../../src/modules/grade/interfaces/evaluation-activity.interface';
import { IEvaluationActivityRepository } from '../interfaces/evaluation-activity.repository.interface';
import { EvaluationActivityEntity } from '../entities/evaluation-activity.entity';

@Injectable()
export class EvaluationActivityRepository
  extends BaseAbstractRepository<EvaluationActivityEntity>
  implements IEvaluationActivityRepository
{
  constructor(
    @InjectRepository(EvaluationActivityEntity)
    evaluationActivityEntity: Repository<EvaluationActivityEntity>,
    @InjectDataSource()
    private readonly dataSource: DataSource,
  ) {
    super(evaluationActivityEntity);
  }

  async queryGroupEvaluationScore(
    group_id: number,
    activity_ids?: number[],
  ): Promise<EvaluationActivityScore[]> {
    let query = `
      select ejr.evaluation_activity_id as activity_id,
          ejr.player_id,
          ea.player_type,
          ep.assign_to_id,
          ea.score_type,
          ea.total_score as activity_setting_score,
          ejr.score,
          ej.is_submit
      from evaluation_activities ea
            inner join evaluation_judge_records ejr
                      on ejr.evaluation_activity_id = ea.id
            inner join evaluation_players ep on ep.id = ejr.player_id
            inner join evaluation_judges ej  on ej.id = ejr.judge_id
      where ea.group_id = $1
      and ea.status = 1
      and ej.is_submit = 2
    `;

    const params: any[] = [group_id];

    // 只有当 activity_ids 存在且不为空时，才添加 ea.id 过滤条件
    if (activity_ids && activity_ids.length > 0) {
      query += ` and ea.id = ANY($2)`;
      params.push(activity_ids);
    }

    query += ` and ejr.score is not null`;

    const result = await this.dataSource.query(query, params);

    return result.map((player: any) => ({
      activity_id: player.activity_id,
      player_id: player.player_id,
      player_type: player.player_type,
      assign_to_id: player.assign_to_id,
      score: isNaN(player.score) ? 0 : Number(player.score),
      activity_setting_score: Number(player.activity_setting_score),
      score_type: player.score_type,
    }));
  }
}
