import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migration1750631257301 implements MigrationInterface {
  name = 'Migration1750631257301';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "books" DROP CONSTRAINT "FK_1056dbee4616479f7d562c562df"
        `);
    await queryRunner.query(`
            ALTER TABLE "authors"
            ADD CONSTRAINT "UQ_d2ed02fabd9b52847ccb85e6b88" UNIQUE ("id")
        `);
    await queryRunner.query(`
            ALTER TABLE "borrowings" DROP CONSTRAINT "FK_b65e33ab3f4fd885a212491011d"
        `);
    await queryRunner.query(`
            ALTER TABLE "books"
            ADD CONSTRAINT "UQ_f3f2f25a099d24e12545b70b022" UNIQUE ("id")
        `);
    await queryRunner.query(`
            ALTER TABLE "borrowings"
            ADD CONSTRAINT "UQ_5da0d5a9a91e8c386e1f6812db2" UNIQUE ("id")
        `);
    await queryRunner.query(`
            ALTER TABLE "books"
            ADD CONSTRAINT "FK_1056dbee4616479f7d562c562df" FOREIGN KEY ("author_id") REFERENCES "authors"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE "borrowings"
            ADD CONSTRAINT "FK_b65e33ab3f4fd885a212491011d" FOREIGN KEY ("book_id") REFERENCES "books"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "borrowings" DROP CONSTRAINT "FK_b65e33ab3f4fd885a212491011d"
        `);
    await queryRunner.query(`
            ALTER TABLE "books" DROP CONSTRAINT "FK_1056dbee4616479f7d562c562df"
        `);
    await queryRunner.query(`
            ALTER TABLE "borrowings" DROP CONSTRAINT "UQ_5da0d5a9a91e8c386e1f6812db2"
        `);
    await queryRunner.query(`
            ALTER TABLE "books" DROP CONSTRAINT "UQ_f3f2f25a099d24e12545b70b022"
        `);
    await queryRunner.query(`
            ALTER TABLE "borrowings"
            ADD CONSTRAINT "FK_b65e33ab3f4fd885a212491011d" FOREIGN KEY ("book_id") REFERENCES "books"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
            ALTER TABLE "authors" DROP CONSTRAINT "UQ_d2ed02fabd9b52847ccb85e6b88"
        `);
    await queryRunner.query(`
            ALTER TABLE "books"
            ADD CONSTRAINT "FK_1056dbee4616479f7d562c562df" FOREIGN KEY ("author_id") REFERENCES "authors"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
  }
}
