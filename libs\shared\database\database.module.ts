import { Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { join } from 'path';
import { DataSourceOptions } from 'typeorm';
import { SnakeNamingStrategy } from 'typeorm-naming-strategies';

export const getTypeormConfig = (config: {
  typeorm: {
    host: string;
    port: number;
    username: string;
    password: string;
    database: string;
    logging: boolean;
  };
}) => {
  return {
    ...config.typeorm,
    type: 'postgres',
    entities: [
      join(__dirname, 'entities', '*{.ts,.js}'),
      join(__dirname, 'views', '*{.ts,.js}'),
    ],
    migrations: [join(__dirname, 'migrations', '*{.ts,.js}')],
    synchronize: false,
    namingStrategy: new SnakeNamingStrategy(),
    ssl:
      process.env.DB_USE_SSL === 'true'
        ? {
            rejectUnauthorized: false,
          }
        : false,
    // 连接池配置
    extra: {
      connectionLimit: 10,
      acquireTimeout: 60000,
      timeout: 60000,
      reconnect: true,
      reconnectTries: 3,
      reconnectInterval: 1000,
    },
    // 连接超时配置
    connectTimeoutMS: 30000,
    acquireTimeoutMillis: 30000,
    // 重试配置
    retryAttempts: 3,
    retryDelay: 3000,
  } as DataSourceOptions;
};

@Module({
  imports: [
    TypeOrmModule.forRootAsync({
      useFactory: (config: ConfigService) => {
        const dbConfig = {
          typeorm: {
            host: config.get('DB_HOST'),
            port: config.get('DB_PORT'),
            username: config.get('DB_USERNAME'),
            password: config.get('DB_PASSWORD'),
            database: config.get('DB_DATABASE'),
            logging: config.get('DB_LOGGING') === 'true',
          },
        };

        return getTypeormConfig(dbConfig);
      },
      inject: [ConfigService],
    }),
  ],
})
export class DatabaseModule {}
