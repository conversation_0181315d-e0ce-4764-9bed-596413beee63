import { Inject, Injectable } from '@nestjs/common';
import { CreateGradeDto } from './dto/create-grade.dto';
import { UpdateGradeDto } from './dto/update-grade.dto';
import { ActivityNodeRepositoryInterface } from '@app/shared/database/interfaces/activity-node.repository.interface';

@Injectable()
export class GradeService {
  constructor(
    @Inject('ActivityNodeRepositoryInterface')
    private readonly activityNodeRepository: ActivityNodeRepositoryInterface,
  ) {}

  create(createGradeDto: CreateGradeDto) {
    return 'This action adds a new grade';
  }

  findAll() {
    return `This action returns all grade`;
  }

  async findOne(id: number) {
    const result = await this.activityNodeRepository.findByCondition({
      where: { name: '图片测试文件-10M.png' },
    });

    return `This action returns a #${id} grade`;
  }

  update(id: number, updateGradeDto: UpdateGradeDto) {
    return `This action updates a #${id} grade`;
  }

  remove(id: number) {
    return `This action removes a #${id} grade`;
  }

  async getCourseGrade(groupId: string, scoringItemTypes: string[]) {}
}
