import { Processor } from '@nestjs/bullmq';
import { BadRequestException, Injectable } from '@nestjs/common';
import { Job } from 'bullmq';
import { COURSE_GRADE_MERGE } from '../constants/grade.constant';
import { CourseGradeDto } from '../dto/get-course-grade.dto';
import { COURSE_GRADE_OPS } from '../enums/course-grade-ops.enum';
import { WorkerHostProcessor } from './worker-host.processor';

@Processor(COURSE_GRADE_MERGE)
@Injectable()
export class CourseGradeMergeProcessor extends WorkerHostProcessor {
  async process(
    job: Job<CourseGradeDto, number | number[], string>,
  ): Promise<number | number[]> {
    const results = Object.values(await job.getChildrenValues());
    switch (job.name) {
      case COURSE_GRADE_OPS.SCORE:
        return [1];
    }

    throw new BadRequestException(`Unknown job name ${job.name}`);
  }
}
