import { Processor } from '@nestjs/bullmq';
import { BadRequestException, Injectable } from '@nestjs/common';
import { Job } from 'bullmq';
import { COURSE_GRADE_CHILD } from '../constants/grade.constant';
import { COURSE_GRADE_OPS } from '../enums/course-grade-ops.enum';
import { ComparisonJobProgress } from '../interface/job-progress.interface';
import { EvaluationActivityJobData } from '../interfaces/evaluation-activity.interface';
import { EvaluationActivityService } from '../services/evaluation-activity.service';
import { WorkerHostProcessor } from './worker-host.processor';

@Processor(COURSE_GRADE_CHILD)
@Injectable()
export class CourseGradeChildProcessor extends WorkerHostProcessor {
  constructor(
    private readonly evaluationActivityService: EvaluationActivityService,
  ) {
    super();
  }

  async process(
    job: Job<
      ComparisonJobProgress | EvaluationActivityJobData,
      number | number[],
      string
    >,
  ): Promise<number | number[]> {
    switch (job.name) {
      case COURSE_GRADE_OPS.EVALUATION_ACTIVITY_SCORE:
        return await this.processEvaluationActivityScore(
          job as Job<EvaluationActivityJobData, number[], string>,
        );
    }

    throw new BadRequestException(
      `Unknown job name ${job.name} found in queue ${job.queueName}`,
    );
  }

  private async processEvaluationActivityScore(
    job: Job<EvaluationActivityJobData, number[], string>,
  ): Promise<number[]> {
    const { group_id, activity_ids, subgroups } = job.data.data;

    // 获取评价活动得分
    const playerScores =
      await this.evaluationActivityService.getGroupEvaluationPlayerScore(
        group_id,
        subgroups,
        activity_ids,
      );

    console.log('playerScores', playerScores);

    // 返回所有最终得分
    const scores = playerScores.map((score) => score.final_score);

    // 更新进度
    job.updateProgress(100);

    return scores;
  }
}
