import * as _ from 'lodash';
import { Inject, Injectable } from '@nestjs/common';
import {
  EvaluationPlayerScore,
  EvaluationScoreWithMembers,
  SubgroupMember,
} from '../interfaces/evaluation-activity.interface';
import {
  EVALUATION_ACTIVITY_REPOSITORY,
  IEvaluationActivityRepository,
} from 'libs/shared/database/interfaces/evaluation-activity.repository.interface';

@Injectable()
export class EvaluationActivityService {
  constructor(
    @Inject(EVALUATION_ACTIVITY_REPOSITORY)
    private readonly evaluationActivityRepository: IEvaluationActivityRepository,
  ) {}

  /**
   * 获取被评对象的得分
   */
  async getGroupEvaluationPlayerScore(
    group_id: number,
    subgroups: Record<string, SubgroupMember[]>,
    activity_ids?: number[],
  ): Promise<EvaluationPlayerScore[]> {
    if (activity_ids && !activity_ids.length) return [];

    const activityPlayerScore =
      await this.evaluationActivityRepository.queryGroupEvaluationScore(
        group_id,
        activity_ids,
      );

    // 计分同一个被评对象,有效的得分成员
    const scoreMembers: EvaluationScoreWithMembers[] = activityPlayerScore.map(
      (player) => {
        const { assign_to_id, player_type } = player;

        // 班级或小组评价
        if (player_type === 3 || player_type === 4) {
          // class or subgroup
          return {
            ...player,
            be_score_user_ids: Object.keys(
              _.groupBy(subgroups[assign_to_id] || [], 'user_id'),
            ),
          };
        } else {
          return {
            ...player,
            be_score_user_ids: [String(assign_to_id)],
          };
        }
      },
    );

    // 根据计分项, 计算被评对象
    const groupedByPlayer = _.groupBy(scoreMembers, 'player_id');

    return Object.values(groupedByPlayer).map(
      (player_scores: EvaluationScoreWithMembers[]) => {
        const firstScore = player_scores[0];
        const {
          activity_id,
          player_id,
          player_type,
          score_type,
          be_score_user_ids,
          assign_to_id,
          activity_setting_score,
        } = firstScore;

        let beScoreCount = 0;
        let totalScore = 0;

        // 评价活动最高分模式下，如果打分的人不满三个人，则走平均分模式
        if (score_type === 3 && _.size(player_scores) >= 3) {
          // avg_exclude_min_max
          const scoreWithOutMaxAndMin = this.pullMaxAndMinScore(player_scores);
          beScoreCount = scoreWithOutMaxAndMin.length;
          totalScore = _.sumBy(scoreWithOutMaxAndMin, 'score');
        } else {
          beScoreCount = _.size(player_scores);
          totalScore = _.sumBy(player_scores, 'score');
        }

        let finalScore = 0;
        if (beScoreCount > 0) {
          finalScore = Number((totalScore / beScoreCount).toFixed(2));
        }

        return {
          activity_id,
          player_id,
          player_type,
          score_type,
          be_score_user_ids,
          assign_to_id,
          final_score: finalScore,
          activity_setting_score,
        };
      },
    );
  }

  /**
   * 最高分和最低分去除后的评分
   */
  private pullMaxAndMinScore(
    items: EvaluationScoreWithMembers[],
  ): EvaluationScoreWithMembers[] {
    if (items.length <= 2) return items;

    const minItem = _.minBy(items, 'score');
    const maxItem = _.maxBy(items, 'score');

    if (!minItem || !maxItem) return items;

    return items.filter((item) => item !== minItem && item !== maxItem);
  }
}
