import { DataGroupingUtil, PlayerScore } from '../utils/DataGroupingUtil';

describe('DataGroupingUtil', () => {
  const mockPlayerScores: PlayerScore[] = [
    {
      be_score_user_ids: ['user1', 'user2'],
      gameId: 'game1',
      score: 100,
      timestamp: 1640995200000
    },
    {
      be_score_user_ids: ['user1', 'user3'],
      gameId: 'game2',
      score: 200,
      timestamp: 1640995300000
    },
    {
      be_score_user_ids: ['user2'],
      gameId: 'game3',
      score: 150,
      timestamp: 1640995400000
    }
  ];

  describe('expandAndGroupBy', () => {
    it('should expand array field and group by specified field', () => {
      const result = DataGroupingUtil.expandAndGroupBy(
        mockPlayerScores,
        'be_score_user_ids',
        'userId'
      );

      expect(result).toHaveProperty('user1');
      expect(result).toHaveProperty('user2');
      expect(result).toHaveProperty('user3');
      
      expect(result.user1).toHaveLength(2);
      expect(result.user2).toHaveLength(2);
      expect(result.user3).toHaveLength(1);
      
      expect(result.user1[0]).toEqual({
        gameId: 'game1',
        score: 100,
        timestamp: 1640995200000
      });
    });

    it('should throw error for non-array field', () => {
      const invalidData = [
        {
          be_score_user_ids: 'not_an_array',
          gameId: 'game1',
          score: 100
        }
      ];

      expect(() => {
        DataGroupingUtil.expandAndGroupBy(
          invalidData as any,
          'be_score_user_ids',
          'userId'
        );
      }).toThrow("Field 'be_score_user_ids' must be an array");
    });

    it('should exclude specified fields from result', () => {
      const result = DataGroupingUtil.expandAndGroupBy(
        mockPlayerScores,
        'be_score_user_ids',
        'userId',
        ['timestamp']
      );

      expect(result.user1[0]).not.toHaveProperty('timestamp');
      expect(result.user1[0]).toHaveProperty('gameId');
      expect(result.user1[0]).toHaveProperty('score');
    });
  });

  describe('groupPlayerScoresByUser', () => {
    it('should group player scores by user ID', () => {
      const result = DataGroupingUtil.groupPlayerScoresByUser(mockPlayerScores);

      expect(result).toHaveProperty('user1');
      expect(result).toHaveProperty('user2');
      expect(result).toHaveProperty('user3');
      
      expect(result.user1).toHaveLength(2);
      expect(result.user2).toHaveLength(2);
      expect(result.user3).toHaveLength(1);
    });
  });

  describe('expandArrayField', () => {
    it('should expand array field with custom field name', () => {
      const result = DataGroupingUtil.expandArrayField(
        mockPlayerScores,
        'be_score_user_ids',
        'playerId'
      );

      expect(result).toHaveLength(5); // total expanded items
      expect(result[0]).toHaveProperty('playerId');
      expect(result[0]).not.toHaveProperty('be_score_user_ids');
      expect(result[0].playerId).toBe('user1');
    });
  });

  describe('groupByMultipleFields', () => {
    it('should group by multiple fields', () => {
      const testData = [
        { category: 'A', type: 'X', value: 1 },
        { category: 'A', type: 'Y', value: 2 },
        { category: 'B', type: 'X', value: 3 },
        { category: 'B', type: 'Y', value: 4 }
      ];

      const result = DataGroupingUtil.groupByMultipleFields(
        testData,
        ['category', 'type']
      );

      expect(result).toHaveProperty('A');
      expect(result).toHaveProperty('B');
      expect(result.A).toHaveProperty('X');
      expect(result.A).toHaveProperty('Y');
      expect(result.B).toHaveProperty('X');
      expect(result.B).toHaveProperty('Y');
      
      expect(result.A.X).toHaveLength(1);
      expect(result.A.X[0].value).toBe(1);
    });

    it('should return original data when no grouping fields provided', () => {
      const testData = [{ id: 1 }, { id: 2 }];
      const result = DataGroupingUtil.groupByMultipleFields(testData, []);
      
      expect(result).toEqual(testData);
    });
  });

  describe('getGroupCounts', () => {
    it('should return count for each group', () => {
      const groupedData = {
        group1: [{ id: 1 }, { id: 2 }],
        group2: [{ id: 3 }],
        group3: [{ id: 4 }, { id: 5 }, { id: 6 }]
      };

      const result = DataGroupingUtil.getGroupCounts(groupedData);

      expect(result).toEqual({
        group1: 2,
        group2: 1,
        group3: 3
      });
    });
  });

  describe('filterGroups', () => {
    it('should filter groups based on predicate', () => {
      const groupedData = {
        group1: [{ id: 1 }, { id: 2 }],
        group2: [{ id: 3 }],
        group3: [{ id: 4 }, { id: 5 }, { id: 6 }]
      };

      // 只保留长度大于1的组
      const result = DataGroupingUtil.filterGroups(
        groupedData,
        (group) => group.length > 1
      );

      expect(result).toHaveProperty('group1');
      expect(result).toHaveProperty('group3');
      expect(result).not.toHaveProperty('group2');
    });

    it('should pass group key to predicate function', () => {
      const groupedData = {
        activeGroup: [{ id: 1 }],
        inactiveGroup: [{ id: 2 }]
      };

      const result = DataGroupingUtil.filterGroups(
        groupedData,
        (group, key) => key.includes('active')
      );

      expect(result).toHaveProperty('activeGroup');
      expect(result).not.toHaveProperty('inactiveGroup');
    });
  });
});
