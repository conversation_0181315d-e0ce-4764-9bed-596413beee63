import { Expose, Type } from 'class-transformer';
import { IsString, IsUrl } from 'class-validator';
import { decorate } from 'ts-mixer';

export class MongoEnvironmentVariables {
  @decorate(Expose())
  @decorate(
    IsUrl({
      protocols: ['mongodb', 'mongodb+srv'],
      require_tld: false,
      require_protocol: true,
      require_valid_protocol: true,
    }),
  )
  @decorate(IsString())
  MONGO_URL: string;
}
