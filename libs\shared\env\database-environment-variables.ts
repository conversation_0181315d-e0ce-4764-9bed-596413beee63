import { Expose, Type } from 'class-transformer';
import { IsString, IsNumber, IsBoolean, IsOptional } from 'class-validator';
import { decorate } from 'ts-mixer';

export class DatabaseEnvironmentVariables {
  @decorate(Expose())
  @decorate(IsString())
  DB_HOST: string;

  @decorate(Expose())
  @decorate(Type(() => Number))
  @decorate(IsNumber())
  DB_PORT: number;

  @decorate(Expose())
  @decorate(IsString())
  DB_USERNAME: string;

  @decorate(Expose())
  @decorate(IsString())
  DB_PASSWORD: string;

  @decorate(Expose())
  @decorate(IsString())
  DB_DATABASE: string;

  @decorate(Expose())
  @decorate(Type(() => Boolean))
  @decorate(IsBoolean())
  @decorate(IsOptional())
  DB_LOGGING?: boolean;

  @decorate(Expose())
  @decorate(IsString())
  @decorate(IsOptional())
  DB_USE_SSL?: string;
}
