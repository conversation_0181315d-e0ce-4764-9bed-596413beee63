import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseAbstractRepository } from './base/base.abstract.repository';
import { GradeScoringItemRepositoryInterface } from '../interfaces/grade-scoring-item.repository.interface';
import { GradeScoringItemEntity } from '../entities/grade-scoring-item.entity';

@Injectable()
export class GradeScoringItemRepository
  extends BaseAbstractRepository<GradeScoringItemEntity>
  implements GradeScoringItemRepositoryInterface
{
  constructor(
    @InjectRepository(GradeScoringItemEntity)
    private readonly gradeScoringItemEntity: Repository<GradeScoringItemEntity>,
  ) {
    super(gradeScoringItemEntity);
  }

  async findByGroupId(groupId: number): Promise<GradeScoringItemEntity[]> {
    return this.gradeScoringItemEntity.find({
      where: { groupId },
      order: { createdAt: 'DESC' },
    });
  }

  async findByCreator(creator: number): Promise<GradeScoringItemEntity[]> {
    return this.gradeScoringItemEntity.find({
      where: { creator },
      order: { createdAt: 'DESC' },
    });
  }

  async findByGroupIdAndType(
    groupId: number,
    scoringItemType: number,
  ): Promise<GradeScoringItemEntity[]> {
    return this.gradeScoringItemEntity.find({
      where: { groupId, scoringItemType },
      order: { createdAt: 'DESC' },
    });
  }

  async findSelectedItems(groupId: number): Promise<GradeScoringItemEntity[]> {
    return this.gradeScoringItemEntity.find({
      where: { groupId, isSelected: true },
      order: { createdAt: 'DESC' },
    });
  }

  async findBySettingType(
    settingType: number,
  ): Promise<GradeScoringItemEntity[]> {
    return this.gradeScoringItemEntity.find({
      where: { settingType },
      order: { createdAt: 'DESC' },
    });
  }

  async findByGroupIdAndSettingType(
    groupId: number,
    settingType: number,
  ): Promise<GradeScoringItemEntity[]> {
    return this.gradeScoringItemEntity.find({
      where: { groupId, settingType },
      order: { createdAt: 'DESC' },
    });
  }
}
