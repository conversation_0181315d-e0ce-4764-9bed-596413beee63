import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AppService } from './app.service';
import { AppController } from './app.controller';
import { MessagesModule } from './modules/messages/messages.module';
import { MessagesRepoModule } from './modules/messages-repo/messages.repo.module';
// import appConfig from 'config/app.config';
import { BullModule } from '@nestjs/bullmq';
import { BullConfigService } from '@app/shared/redis';
import { EnvironmentVariables } from './env';
import { validate } from '@app/shared/env';
import { bullboardConfig } from './modules/queue-board/bull-board.config';
import { MathModule } from './modules/math/math.module';
import { GradeModule } from './modules/grade/grade.module';
import { DatabaseModule } from '@app/shared/database/database.module';

@Module({
  imports: [
    // ConfigModule.forRoot({
    //   envFilePath: ['config/.env.prod', 'config/.env.dev'],
    //   isGlobal: true,
    //   validate: validate(EnvironmentVariables),
    // }),
    ConfigModule.forRoot({
      cache: true,
      isGlobal: true,
      expandVariables: true,
      validate: validate(EnvironmentVariables),
    }),
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => {
        return { uri: configService.get<string>('MONGO_URL') };
      },
    }),
    BullModule.forRootAsync({
      inject: [ConfigService],
      useClass: BullConfigService,
    }),
    bullboardConfig,
    DatabaseModule,
    MessagesRepoModule,
    MessagesModule,
    MathModule,
    GradeModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
