import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseAbstractRepository } from './base/base.abstract.repository';
import { ActivityNodeRepositoryInterface } from '../interfaces/activity-node.repository.interface';
import { ActivityNodeEntity } from '../entities/activity-node.entity';

@Injectable()
export class ActivityNodeRepository
  extends BaseAbstractRepository<ActivityNodeEntity>
  implements ActivityNodeRepositoryInterface
{
  constructor(
    @InjectRepository(ActivityNodeEntity)
    private readonly activityNodeEntity: Repository<ActivityNodeEntity>,
  ) {
    super(activityNodeEntity);
  }
}
