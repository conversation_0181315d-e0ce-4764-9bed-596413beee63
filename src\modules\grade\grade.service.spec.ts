import { Test, TestingModule } from '@nestjs/testing';
import { GradeService } from './grade.service';
import { ActivityNodeRepositoryInterface } from '@app/shared/database/interfaces/activity-node.repository.interface';

// Mock implementation of ActivityNodeRepositoryInterface
const mockActivityNodeRepository = {
  findByCondition: jest.fn(),
  // Add other methods as needed based on the interface
};

describe('GradeService', () => {
  let service: GradeService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        GradeService,
        {
          provide: 'ActivityNodeRepositoryInterface',
          useValue: mockActivityNodeRepository,
        },
      ],
    }).compile();

    service = module.get<GradeService>(GradeService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
