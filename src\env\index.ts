import { ConfigService } from '@nestjs/config';
import {
  BaseEnvironmentVariables,
  RedisEnvironmentVariables,
  MongoEnvironmentVariables,
  KafkaEnvironmentVariables,
  DatabaseEnvironmentVariables,
} from '@app/shared/env';
import { Mixin } from 'ts-mixer';

export type AppConfigService = ConfigService<EnvironmentVariables, true>;

export class EnvironmentVariables extends Mixin(
  BaseEnvironmentVariables,
  RedisEnvironmentVariables,
  MongoEnvironmentVariables,
  KafkaEnvironmentVariables,
  DatabaseEnvironmentVariables,
) {
  APP_NAME?: string = 'message';
}
