import { Injectable } from '@nestjs/common';
import { FlowChildJob, FlowProducer } from 'bullmq';
import {
  COURSE_GRADE_MERGE,
  COURSE_GRADE_CHILD,
} from '../constants/grade.constant';
import { InjectCourseGradeProducer } from '../decorators/inject-flow-producer.decorator';
import { CourseGradeDto } from '../dto/get-course-grade.dto';
import { COURSE_GRADE_OPS } from '../enums/course-grade-ops.enum';

@Injectable()
export class GradeFlowService {
  constructor(
    @InjectCourseGradeProducer() private courseGradeFlowProducer: FlowProducer,
  ) {}

  async createFlow(
    dto: CourseGradeDto,
    jobName: COURSE_GRADE_OPS,
  ): Promise<string> {
    const children = this.createChildrenJobs(
      dto,
      COURSE_GRADE_OPS.EVALUATION_ACTIVITY_SCORE,
    );

    const flow = await this.courseGradeFlowProducer.add({
      name: jobName,
      queueName: COURSE_GRADE_MERGE,
      children,
      opts: {
        failParentOnFailure: true,
      },
    });
    return flow.job.id || '';
  }

  private createChildrenJobs(dto: CourseGradeDto, jobName: COURSE_GRADE_OPS) {
    const children: FlowChildJob[] = [];

    children.push({
      name: jobName,
      data: { data: dto },
      queueName: COURSE_GRADE_CHILD,
    });

    return children;
  }
}
