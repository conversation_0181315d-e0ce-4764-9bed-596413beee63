import _ from 'lodash';

/**
 * 通用数据分组工具类
 * 用于将包含数组字段的数据展开并按指定字段分组
 */
export class DataGroupingUtil {
  /**
   * 将包含数组字段的数据展开并按指定字段分组
   * @param data 原始数据数组
   * @param arrayField 需要展开的数组字段名
   * @param groupByField 分组依据的字段名（展开后的字段）
   * @param excludeFields 需要从结果中排除的字段名数组
   * @returns 按指定字段分组的对象
   */
  static expandAndGroupBy<T extends Record<string, any>, K extends keyof T>(
    data: T[],
    arrayField: K,
    groupByField: string = 'id',
    excludeFields: string[] = []
  ): Record<string, Omit<T, K>[]> {
    return _.chain(data)
      .flatMap((item) => {
        const arrayValue = item[arrayField] as unknown as any[];
        const restData = _.omit(item, [arrayField, ...excludeFields]) as Omit<T, K>;
        
        if (!Array.isArray(arrayValue)) {
          throw new Error(`Field '${String(arrayField)}' must be an array`);
        }
        
        return arrayValue.map((value) => ({
          [groupByField]: value,
          ...restData,
        }));
      })
      .groupBy(groupByField)
      .mapValues((items) => items.map(item => _.omit(item, groupByField)))
      .value();
  }

  /**
   * 专门用于用户分数数据的分组方法
   * @param playerScores 玩家分数数据数组
   * @returns 按用户ID分组的分数数据
   */
  static groupPlayerScoresByUser<T extends { be_score_user_ids: string[] }>(
    playerScores: T[]
  ): Record<string, Omit<T, 'be_score_user_ids'>[]> {
    return this.expandAndGroupBy(
      playerScores,
      'be_score_user_ids',
      'userId'
    );
  }

  /**
   * 通用的数组字段展开方法
   * @param data 原始数据数组
   * @param arrayField 需要展开的数组字段名
   * @param newFieldName 展开后新字段的名称
   * @returns 展开后的数据数组
   */
  static expandArrayField<T extends Record<string, any>, K extends keyof T>(
    data: T[],
    arrayField: K,
    newFieldName: string = 'item'
  ): (Omit<T, K> & Record<string, any>)[] {
    return _.flatMap(data, (item) => {
      const arrayValue = item[arrayField] as unknown as any[];
      const restData = _.omit(item, arrayField) as Omit<T, K>;
      
      if (!Array.isArray(arrayValue)) {
        throw new Error(`Field '${String(arrayField)}' must be an array`);
      }
      
      return arrayValue.map((value) => ({
        [newFieldName]: value,
        ...restData,
      }));
    });
  }

  /**
   * 按多个字段进行分组
   * @param data 数据数组
   * @param groupByFields 分组字段数组
   * @returns 多层嵌套的分组结果
   */
  static groupByMultipleFields<T extends Record<string, any>>(
    data: T[],
    groupByFields: (keyof T)[]
  ): any {
    if (groupByFields.length === 0) {
      return data;
    }

    const [firstField, ...restFields] = groupByFields;
    const grouped = _.groupBy(data, firstField as string);

    if (restFields.length === 0) {
      return grouped;
    }

    return _.mapValues(grouped, (group) =>
      this.groupByMultipleFields(group, restFields)
    );
  }

  /**
   * 统计分组后每组的数量
   * @param groupedData 分组后的数据
   * @returns 每组的统计信息
   */
  static getGroupCounts<T>(groupedData: Record<string, T[]>): Record<string, number> {
    return _.mapValues(groupedData, (group) => group.length);
  }

  /**
   * 过滤分组数据
   * @param groupedData 分组后的数据
   * @param predicate 过滤条件
   * @returns 过滤后的分组数据
   */
  static filterGroups<T>(
    groupedData: Record<string, T[]>,
    predicate: (group: T[], key: string) => boolean
  ): Record<string, T[]> {
    return _.pickBy(groupedData, predicate);
  }
}

// 类型定义
export interface PlayerScore {
  be_score_user_ids: string[];
  gameId: string;
  score: number;
  timestamp?: number;
  [key: string]: any;
}

export interface GroupingOptions {
  arrayField: string;
  groupByField?: string;
  excludeFields?: string[];
}

// 使用示例的类型
export interface ExampleUsage {
  // 基本用法示例
  basic: () => void;
  // 高级用法示例
  advanced: () => void;
}
