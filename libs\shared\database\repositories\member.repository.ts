import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { BaseAbstractRepository } from './base/base.abstract.repository';
import { MemberRepositoryInterface } from '../interfaces/member.repository.interface';
import { MemberEntity } from '../entities/member.entity';

@Injectable()
export class MemberRepository
  extends BaseAbstractRepository<MemberEntity>
  implements MemberRepositoryInterface
{
  constructor(
    @InjectRepository(MemberEntity)
    private readonly memberRepository: Repository<MemberEntity>,
  ) {
    super(memberRepository);
  }

  /**
   * 根据组ID获取所有成员
   */
  async findByGroupId(groupId: number): Promise<MemberEntity[]> {
    return this.memberRepository.find({
      where: { groupId },
    });
  }

  /**
   * 根据用户ID列表获取成员信息
   */
  async findByUserIds(userIds: number[]): Promise<MemberEntity[]> {
    if (userIds.length === 0) return [];

    return this.memberRepository.find({
      where: { userId: In(userIds) },
    });
  }

  /**
   * 根据组ID和用户ID列表获取成员信息
   */
  async findByGroupAndUserIds(
    groupId: number,
    userIds: number[],
  ): Promise<MemberEntity[]> {
    if (userIds.length === 0) return [];

    return this.memberRepository.find({
      where: {
        groupId,
        userId: In(userIds),
      },
    });
  }

  /**
   * 获取组内活跃成员（未被禁用）
   */
  async findActiveMembers(groupId: number): Promise<MemberEntity[]> {
    return this.memberRepository.find({
      where: {
        groupId,
        ban: -1, // 未被禁用
        status: 1, // 假设1为活跃状态
      },
    });
  }

  /**
   * 根据组ID和角色获取成员
   */
  async findByGroupIdAndRole(
    groupId: number,
    role: number,
  ): Promise<MemberEntity[]> {
    return this.memberRepository.find({
      where: {
        groupId,
        role,
      },
    });
  }

  /**
   * 批量获取多个组的成员信息
   */
  async findByGroupIds(groupIds: number[]): Promise<MemberEntity[]> {
    if (groupIds.length === 0) return [];

    return this.memberRepository.find({
      where: {
        groupId: In(groupIds),
      },
    });
  }
}
