import { Modu<PERSON> } from '@nestjs/common';
import { QueueModule } from '../queue-board/queue-board.module';
import { MATH_BINARY } from './constants/math.constant';
import { MATH_ARRAY_PRODUCER } from './constants/math-array.constant';
import { MathBinaryOperationProcessor } from './processors/math-binary-operation.processor';
import { MathController } from './controllers/math.controller';

@Module({
  imports: [
    QueueModule.register({
      queues: [MATH_BINARY],
      flows: [],
    }),
  ],
  controllers: [MathController],
  providers: [MathBinaryOperationProcessor],
})
export class MathModule {}
