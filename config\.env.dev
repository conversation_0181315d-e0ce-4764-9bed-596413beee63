APP_PORT=3000
APP_ENV=test
DOC_USER=usr
DOC_PASS=pass
MONGO_URL=******************************************************************************
KAFKA_URI='localhost:9092'
KAFKA_RAW_MESSAGE_TOPIC='raw-message'
KAFKA_RAW_MESSAGE_GROUP='raw-messages'
KAFKA_READY_MESSAGE_TOPIC='ready-message'
KAFKA_ANALYSIS_MESSAGE_TOPIC='analysis-message'
KAFKA_ANALYSIS_MESSAGE_GROUP='analysis-messages'

#postgresSQL
DB_HOST=**************
DB_DATABASE=aia-base-testing
DB_USER=dbadmin
DB_PASSWORD=ZEg4CPXnBWMsHDwCdTuRPub6MuMwGW3i
DB_PORT=5432
DB_LOGGING=true


REDIS_URL=redis://127.0.0.1:6379
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
