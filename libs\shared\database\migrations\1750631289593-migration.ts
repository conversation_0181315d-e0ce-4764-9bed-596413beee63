import { MigrationInterface, QueryRunner } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';

export class Migration1750631289593 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Generate UUIDs for authors
    const authorIds = {
      rowling: uuidv4(),
      martin: uuidv4(),
      king: uuidv4(),
      christie: uuidv4(),
      murakami: uuidv4(),
    };

    // Insert Authors
    await queryRunner.query(`
        INSERT INTO authors (id, name, biography, birth_date, nationality, website, email, status, created_at, updated_at) VALUES
        ('${authorIds.rowling}', '<PERSON><PERSON><PERSON><PERSON>', 'British author best known for the Harry Potter series', '1965-07-31', 'British', 'https://www.jkrowling.com', '<EMAIL>', 'active', NOW(), NOW()),
        ('${authorIds.martin}', '<PERSON>', 'American novelist and short story writer', '1948-09-20', 'American', 'https://georgerrmartin.com', '<EMAIL>', 'active', NOW(), NOW()),
        ('${authorIds.king}', '<PERSON>', 'American author of horror, supernatural fiction, suspense, and fantasy novels', '1947-09-21', 'American', 'https://stephenking.com', '<EMAIL>', 'active', NOW(), NOW()),
        ('${authorIds.christie}', 'Agatha Christie', 'English writer known for her detective novels', '1890-09-15', 'British', NULL, NULL, 'inactive', NOW(), NOW()),
        ('${authorIds.murakami}', 'Haruki Murakami', 'Japanese writer whose works have been translated into over 50 languages', '1949-01-12', 'Japanese', 'https://www.harukimurakami.com', '<EMAIL>', 'active', NOW(), NOW())
      `);

    // Generate UUIDs for books
    const bookIds = {
      hp1: uuidv4(),
      hp2: uuidv4(),
      got1: uuidv4(),
      shining: uuidv4(),
      orient: uuidv4(),
      norwegian: uuidv4(),
      q84: uuidv4(),
      stand: uuidv4(),
    };

    // Insert Books with unique ISBNs
    await queryRunner.query(`
        INSERT INTO books (id, title, isbn, price, price_currency, description, publication_date, genre, stock_quantity, cover_image_url, status, author_id, created_at, updated_at) VALUES
        ('${bookIds.hp1}', 'Harry Potter and the Philosopher''s Stone', '9780747532699', 12.99, 'USD', 'The first novel in the Harry Potter series', '1997-06-26', 'Fantasy', 15, 'https://example.com/hp1.jpg', 'active', '${authorIds.rowling}', NOW(), NOW()),
        ('${bookIds.hp2}', 'Harry Potter and the Chamber of Secrets', '9780747538493', 13.99, 'USD', 'The second novel in the Harry Potter series', '1998-07-02', 'Fantasy', 12, 'https://example.com/hp2.jpg', 'active', '${authorIds.rowling}', NOW(), NOW()),
        ('${bookIds.got1}', 'A Game of Thrones', '9780553103540', 15.99, 'USD', 'The first novel in A Song of Ice and Fire series', '1996-08-01', 'Fantasy', 8, 'https://example.com/got1.jpg', 'active', '${authorIds.martin}', NOW(), NOW()),
        ('${bookIds.shining}', 'The Shining', '9780385121675', 14.99, 'USD', 'A horror novel about a family''s winter at an isolated hotel', '1977-01-28', 'Horror', 10, 'https://example.com/shining.jpg', 'active', '${authorIds.king}', NOW(), NOW()),
        ('${bookIds.orient}', 'Murder on the Orient Express', '9780062073495', 11.99, 'USD', 'A detective novel featuring Hercule Poirot', '1934-01-01', 'Mystery', 6, 'https://example.com/orient.jpg', 'active', '${authorIds.christie}', NOW(), NOW()),
        ('${bookIds.norwegian}', 'Norwegian Wood', '9780375704024', 16.99, 'USD', 'A novel about loss and sexuality', '1987-09-04', 'Literary Fiction', 9, 'https://example.com/norwegian.jpg', 'active', '${authorIds.murakami}', NOW(), NOW()),
        ('${bookIds.q84}', '1Q84', '9780307593313', 18.99, 'USD', 'A complex novel set in a parallel world', '2009-05-29', 'Literary Fiction', 5, 'https://example.com/1q84.jpg', 'active', '${authorIds.murakami}', NOW(), NOW()),
        ('${bookIds.stand}', 'The Stand', '9780385121676', 17.99, 'USD', 'A post-apocalyptic horror novel', '1978-10-03', 'Horror', 7, 'https://example.com/stand.jpg', 'active', '${authorIds.king}', NOW(), NOW())
      `);

    // Generate UUIDs for borrowings
    const borrowingIds = {
      borrow1: uuidv4(),
      borrow2: uuidv4(),
      borrow3: uuidv4(),
      borrow4: uuidv4(),
      borrow5: uuidv4(),
      borrow6: uuidv4(),
      borrow7: uuidv4(),
      borrow8: uuidv4(),
    };

    // Insert Borrowings
    await queryRunner.query(`
        INSERT INTO borrowings (id, customer_email, customer_phone, customer_name, borrow_date, due_date, return_date, status, fine_amount, notes, book_id, delivery_address, created_at, updated_at) VALUES
        ('${borrowingIds.borrow1}', '<EMAIL>', '+1234567890', 'John Doe', '2024-01-15', '2024-02-15', '2024-02-10', 'returned', 0.00, 'Returned in good condition', '${bookIds.hp1}', '123 Main St, Anytown, USA', NOW(), NOW()),
        ('${borrowingIds.borrow2}', '<EMAIL>', '+1234567891', 'Jane Smith', '2024-01-20', '2024-02-20', NULL, 'borrowed', 0.00, 'Currently borrowed', '${bookIds.got1}', '456 Oak Ave, Somewhere, USA', NOW(), NOW()),
        ('${borrowingIds.borrow3}', '<EMAIL>', '+1234567892', 'Bob Wilson', '2024-01-10', '2024-02-10', NULL, 'overdue', 5.50, 'Overdue by 5 days', '${bookIds.orient}', '789 Pine Rd, Elsewhere, USA', NOW(), NOW()),
        ('${borrowingIds.borrow4}', '<EMAIL>', '+1234567893', 'Alice Brown', '2024-01-05', '2024-02-05', '2024-01-25', 'returned', 0.00, 'Early return', '${bookIds.q84}', '321 Elm St, Nowhere, USA', NOW(), NOW()),
        ('${borrowingIds.borrow5}', '<EMAIL>', '+1234567894', 'Charlie Davis', '2024-01-12', '2024-02-12', NULL, 'lost', 25.00, 'Book reported lost by customer', '${bookIds.hp2}', '654 Maple Dr, Anywhere, USA', NOW(), NOW()),
        ('${borrowingIds.borrow6}', '<EMAIL>', '+1234567895', 'Diana Evans', '2024-01-18', '2024-02-18', NULL, 'borrowed', 0.00, 'Currently borrowed', '${bookIds.shining}', '987 Cedar Ln, Someplace, USA', NOW(), NOW()),
        ('${borrowingIds.borrow7}', '<EMAIL>', '+1234567896', 'Edward Frank', '2024-01-08', '2024-02-08', '2024-02-01', 'returned', 0.00, 'Returned on time', '${bookIds.norwegian}', '147 Birch Way, Everywhere, USA', NOW(), NOW()),
        ('${borrowingIds.borrow8}', '<EMAIL>', '+1234567897', 'Fiona Garcia', '2024-01-22', '2024-02-22', NULL, 'borrowed', 0.00, 'Currently borrowed', '${bookIds.stand}', '258 Spruce Ct, Nowhere, USA', NOW(), NOW())
      `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove borrowings first (due to foreign key constraints)
    await queryRunner.query(`
    DELETE FROM borrowings WHERE customer_email IN (
      '<EMAIL>', '<EMAIL>', '<EMAIL>', 
      '<EMAIL>', '<EMAIL>', '<EMAIL>', 
      '<EMAIL>', '<EMAIL>'
    )
  `);

    // Remove books by ISBN
    await queryRunner.query(`
    DELETE FROM books WHERE isbn IN (
      '9780747532699', '9780747538493', '9780553103540', '9780385121675',
      '9780062073495', '9780375704024', '9780307593313', '9780385121676'
    )
  `);

    // Remove authors by name
    await queryRunner.query(`
    DELETE FROM authors WHERE name IN (
      'J.K. Rowling', 'George R.R. Martin', 'Stephen King', 'Agatha Christie', 'Haruki Murakami'
    )
  `);
  }
}
