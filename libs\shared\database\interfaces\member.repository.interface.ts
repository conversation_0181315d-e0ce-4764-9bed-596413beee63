import { BaseInterfaceRepository } from '../repositories/base/base.interface.repository';
import { MemberEntity } from '../entities/member.entity';

export interface MemberRepositoryInterface
  extends BaseInterfaceRepository<MemberEntity> {
  findByGroupId(groupId: number): Promise<MemberEntity[]>;
  findByUserIds(userIds: number[]): Promise<MemberEntity[]>;
  findByGroupAndUserIds(
    groupId: number,
    userIds: number[],
  ): Promise<MemberEntity[]>;
  findActiveMembers(groupId: number): Promise<MemberEntity[]>;
  findByGroupIdAndRole(groupId: number, role: number): Promise<MemberEntity[]>;
  findByGroupIds(groupIds: number[]): Promise<MemberEntity[]>;
}

export const MEMBER_REPOSITORY = 'MemberRepositoryInterface';
