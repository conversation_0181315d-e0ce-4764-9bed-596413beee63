import * as <PERSON><PERSON> from 'joi';

const configSchema = Joi.object({
  environment: Joi.string()
    .valid('development', 'production', 'test')
    .default('development'),
  database: Joi.object({
    host: Joi.string().hostname().default('localhost'),
    port: Joi.number().integer().min(1).max(65535).default(5432),
    user: Joi.string().min(3).required(),
    password: Joi.string().min(6).required(),
    name: Joi.string().min(3).required(),
  }).required(),
  mongoURL: Joi.string().required(),
});

export default () => {
  const config = {
    environment: process.env.APP_ENV || 'development',
    database: {
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT as string, 10) || 5432,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      name: process.env.DB_DATABASE,
    },
    mongoURL: process.env.MONGO_URL,
  };

  // Validate the configuration against the defined schema
  const { error, value } = configSchema.validate(config, { abortEarly: false });

  // If validation fails, throw an error with the validation messages
  if (error) {
    throw new Error(`Config validation error: ${error.message}`);
  }

  return value;
};
