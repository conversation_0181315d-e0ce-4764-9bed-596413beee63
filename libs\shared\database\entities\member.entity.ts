import { Entity, Column } from 'typeorm';
import { CustomBaseEntity } from './base.entity';

@Entity('members')
export class MemberEntity extends CustomBaseEntity {
  @Column({ type: 'bigint', name: 'group_id' })
  groupId: number;

  @Column({ type: 'varchar', nullable: true })
  nickname: string;

  @Column({ type: 'varchar', nullable: true })
  remark: string;

  @Column({ type: 'varchar', name: 'student_number', nullable: true })
  studentNumber: string;

  @Column({ type: 'integer', nullable: true })
  status: number;

  @Column({ type: 'integer', nullable: true, default: 1 })
  role: number;

  @Column({ type: 'bigint', name: 'user_id' })
  userId: number;

  @Column({ type: 'varchar', nullable: true })
  description: string;

  @Column({ type: 'integer', nullable: true, default: -1 })
  ban: number;

  @Column({ type: 'varchar', nullable: true })
  unionid: string;

  @Column({
    type: 'timestamp with time zone',
    name: 'join_time',
    nullable: true,
  })
  joinTime: Date;

  @Column({
    type: 'timestamp with time zone',
    name: 'last_join_time',
    nullable: true,
  })
  lastJoinTime: Date;
}
