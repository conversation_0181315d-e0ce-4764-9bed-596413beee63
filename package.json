{"name": "micro-message", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@bull-board/api": "^6.12.0", "@bull-board/express": "^6.12.0", "@bull-board/nestjs": "^6.12.0", "@nestjs/bullmq": "^11.0.3", "@nestjs/common": "^10.0.5", "@nestjs/config": "^3.0.0", "@nestjs/core": "^10.0.5", "@nestjs/mongoose": "^10.0.0", "@nestjs/platform-express": "^10.0.5", "@nestjs/swagger": "^7.1.1", "@nestjs/typeorm": "^11.0.0", "bullmq": "^5.56.6", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "express-basic-auth": "^1.2.1", "fast-xml-parser": "^4.4.0", "joi": "^17.9.2", "kafkajs": "^2.2.4", "micro-dto": "github:Igorok/micro-dto#main", "mongoose": "^7.3.2", "npm": "^9.8.0", "pg": "^8.16.3", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "swagger-ui-express": "^5.0.0", "ts-mixer": "^6.0.4", "typeorm": "^0.3.25", "typeorm-naming-strategies": "^4.1.0"}, "devDependencies": {"@nestjs/cli": "^10.1.7", "@nestjs/schematics": "^10.0.1", "@nestjs/testing": "^10.0.5", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.4.1", "@types/supertest": "^2.0.12", "@typescript-eslint/eslint-plugin": "^5.61.0", "@typescript-eslint/parser": "^5.61.0", "eslint": "^8.44.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "jest": "^29.6.1", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-loader": "^9.4.4", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.6"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}