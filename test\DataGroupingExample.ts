import { DataGroupingUtil, PlayerScore } from '../utils/DataGroupingUtil';

// 示例数据
const playerScores: PlayerScore[] = [
  {
    be_score_user_ids: ['user1', 'user2'],
    gameId: 'game1',
    score: 100,
    timestamp: 1640995200000,
    level: 'easy'
  },
  {
    be_score_user_ids: ['user1', 'user3'],
    gameId: 'game2',
    score: 200,
    timestamp: 1640995300000,
    level: 'medium'
  },
  {
    be_score_user_ids: ['user2', 'user3', 'user4'],
    gameId: 'game3',
    score: 150,
    timestamp: 1640995400000,
    level: 'hard'
  }
];

// 其他类型的示例数据
interface OrderData {
  customer_ids: string[];
  orderId: string;
  amount: number;
  product: string;
}

const orders: OrderData[] = [
  {
    customer_ids: ['cust1', 'cust2'],
    orderId: 'order1',
    amount: 299.99,
    product: 'laptop'
  },
  {
    customer_ids: ['cust1', 'cust3'],
    orderId: 'order2',
    amount: 99.99,
    product: 'mouse'
  }
];

/**
 * 使用示例
 */
export class DataGroupingExample {
  /**
   * 基本用法：处理玩家分数数据
   */
  static basicPlayerScoreExample(): void {
    console.log('=== 基本用法：玩家分数分组 ===');
    
    // 使用专门的方法
    const groupedScores = DataGroupingUtil.groupPlayerScoresByUser(playerScores);
    console.log('按用户分组的分数:', JSON.stringify(groupedScores, null, 2));
    
    // 统计每个用户的游戏次数
    const userGameCounts = DataGroupingUtil.getGroupCounts(groupedScores);
    console.log('每个用户的游戏次数:', userGameCounts);
  }

  /**
   * 通用方法使用示例
   */
  static genericMethodExample(): void {
    console.log('\n=== 通用方法使用示例 ===');
    
    // 处理订单数据
    const groupedOrders = DataGroupingUtil.expandAndGroupBy(
      orders,
      'customer_ids',
      'customerId'
    );
    console.log('按客户分组的订单:', JSON.stringify(groupedOrders, null, 2));
  }

  /**
   * 数组字段展开示例
   */
  static arrayExpansionExample(): void {
    console.log('\n=== 数组字段展开示例 ===');
    
    const expandedScores = DataGroupingUtil.expandArrayField(
      playerScores,
      'be_score_user_ids',
      'userId'
    );
    console.log('展开后的分数数据:', JSON.stringify(expandedScores.slice(0, 3), null, 2));
  }

  /**
   * 多字段分组示例
   */
  static multipleFieldGroupingExample(): void {
    console.log('\n=== 多字段分组示例 ===');
    
    // 先展开数据
    const expandedData = DataGroupingUtil.expandArrayField(
      playerScores,
      'be_score_user_ids',
      'userId'
    );
    
    // 按用户ID和等级进行多层分组
    const multiGrouped = DataGroupingUtil.groupByMultipleFields(
      expandedData,
      ['userId', 'level']
    );
    console.log('多字段分组结果:', JSON.stringify(multiGrouped, null, 2));
  }

  /**
   * 过滤分组示例
   */
  static filterGroupsExample(): void {
    console.log('\n=== 过滤分组示例 ===');
    
    const groupedScores = DataGroupingUtil.groupPlayerScoresByUser(playerScores);
    
    // 只保留游戏次数大于1的用户
    const activeUsers = DataGroupingUtil.filterGroups(
      groupedScores,
      (group, userId) => group.length > 1
    );
    console.log('活跃用户（游戏次数>1）:', JSON.stringify(activeUsers, null, 2));
  }

  /**
   * 错误处理示例
   */
  static errorHandlingExample(): void {
    console.log('\n=== 错误处理示例 ===');
    
    const invalidData = [
      {
        be_score_user_ids: 'not_an_array', // 错误：不是数组
        gameId: 'game1',
        score: 100
      }
    ];
    
    try {
      DataGroupingUtil.expandAndGroupBy(
        invalidData as any,
        'be_score_user_ids',
        'userId'
      );
    } catch (error) {
      console.log('捕获到错误:', (error as Error).message);
    }
  }

  /**
   * 运行所有示例
   */
  static runAllExamples(): void {
    this.basicPlayerScoreExample();
    this.genericMethodExample();
    this.arrayExpansionExample();
    this.multipleFieldGroupingExample();
    this.filterGroupsExample();
    this.errorHandlingExample();
  }
}

// 如果直接运行此文件，执行所有示例
if (require.main === module) {
  DataGroupingExample.runAllExamples();
}
