import { Ka<PERSON><PERSON>, Producer, Consumer, KafkaMessage, Partitioners } from 'kafkajs';
import { ConfigService } from '@nestjs/config';
import {
  Controller,
  OnModuleDestroy,
  OnModuleInit,
  Logger,
} from '@nestjs/common';
import { MessagesService } from './messages.service';
import { MessageWebDto, messageAnalysisDto } from 'micro-dto';

@Controller()
export class KafkaController implements OnModuleInit, OnModuleDestroy {
  constructor(
    private configService: ConfigService,
    private messagesService: MessagesService,
  ) {}

  private readonly logger = new Logger(KafkaController.name);

  private readonly kafka: Kafka = new Kafka({
    clientId: 'messages',
    brokers: [this.configService.get<string>('KAFKA_URI')],
    retry: {
      initialRetryTime: 100,
      retries: 8,
    },
    connectionTimeout: 3000,
    requestTimeout: 30000,
  });

  private readonly producer: Producer = this.kafka.producer({
    createPartitioner: Partitioners.LegacyPartitioner,
  });
  private readonly consumer: Consumer = this.kafka.consumer({
    groupId: this.configService.get<string>('KAFKA_RAW_MESSAGE_GROUP'),
  });
  private readonly analysisConsumer: Consumer = this.kafka.consumer({
    groupId: this.configService.get<string>('KAFKA_ANALYSIS_MESSAGE_GROUP'),
  });

  async onModuleInit() {
    try {
      // 创建 admin 客户端来管理 topics
      const admin = this.kafka.admin();
      await admin.connect();

      // 定义需要的 topics
      const topics = [
        {
          topic: this.configService.get<string>('KAFKA_RAW_MESSAGE_TOPIC'),
          numPartitions: 3,
          replicationFactor: 1,
        },
        {
          topic: this.configService.get<string>('KAFKA_READY_MESSAGE_TOPIC'),
          numPartitions: 3,
          replicationFactor: 1,
        },
        {
          topic: this.configService.get<string>('KAFKA_ANALYSIS_MESSAGE_TOPIC'),
          numPartitions: 3,
          replicationFactor: 1,
        },
      ];

      // 检查并创建 topics
      const existingTopics = await admin.listTopics();
      const topicsToCreate = topics.filter(
        (topic) => !existingTopics.includes(topic.topic),
      );

      if (topicsToCreate.length > 0) {
        this.logger.log(
          `Creating topics: ${topicsToCreate.map((t) => t.topic).join(', ')}`,
        );
        await admin.createTopics({
          topics: topicsToCreate,
        });
        this.logger.log('Topics created successfully');
      }

      await admin.disconnect();

      // 连接生产者和消费者
      await this.producer.connect();
      this.logger.log('Producer connected');

      await this.consumer.connect();
      await this.consumer.subscribe({
        topic: this.configService.get<string>('KAFKA_RAW_MESSAGE_TOPIC'),
        fromBeginning: true,
      });
      this.logger.log('Raw message consumer subscribed');

      await this.analysisConsumer.connect();
      await this.analysisConsumer.subscribe({
        topic: this.configService.get<string>('KAFKA_ANALYSIS_MESSAGE_TOPIC'),
        fromBeginning: true,
      });
      this.logger.log('Analysis consumer subscribed');

      await this.consumer.run({
        eachMessage: async ({ message }) => {
          this.receiveMessage(message);
        },
      });

      await this.analysisConsumer.run({
        eachMessage: async ({ message }) => {
          this.receiveAnalysis(message);
        },
      });

      this.logger.log('Kafka controller initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize Kafka controller:', error);
      throw error;
    }
  }

  async onModuleDestroy() {
    try {
      await this.producer.disconnect();
      await this.consumer.disconnect();
      await this.analysisConsumer.disconnect();
    } catch (error) {
      this.logger.error(error);
    }
  }

  async receiveMessage(params: KafkaMessage) {
    try {
      const messageValue = JSON.parse(params.value.toString());
      const { uuid, message, room_id, user_id, created_at } = messageValue;
      const readyMessage: MessageWebDto =
        await this.messagesService.receiveMessage({
          uuid,
          message,
          room_id,
          user_id,
          created_at,
        });

      await this.producer.send({
        topic: this.configService.get<string>('KAFKA_READY_MESSAGE_TOPIC'),
        messages: [
          {
            key: room_id,
            value: JSON.stringify(readyMessage),
          },
        ],
      });
    } catch (error) {
      this.logger.error(error);
    }
  }

  async receiveAnalysis(params: KafkaMessage) {
    try {
      const messageValue: { id: string; analysis: messageAnalysisDto } =
        JSON.parse(params.value.toString());
      const { id, analysis } = messageValue;
      await this.messagesService.receiveAnalysis({ id, analysis });
    } catch (error) {
      this.logger.error(error);
    }
  }
}
