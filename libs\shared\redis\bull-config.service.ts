import {
  BullRootModuleOptions,
  SharedBullConfigurationFactory,
} from '@nestjs/bullmq';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  BaseEnvironmentVariables,
  RedisEnvironmentVariables,
} from '@app/shared/env';

import { RedisConfig } from './redis.config';

@Injectable()
export class BullConfigService<
  T extends RedisEnvironmentVariables & BaseEnvironmentVariables,
> implements SharedBullConfigurationFactory
{
  private readonly redisConfig: RedisConfig<T>;

  constructor(private readonly configService: ConfigService<T, true>) {
    this.redisConfig = new RedisConfig(this.configService, {
      lazyConnect: true,
    });
  }

  createBullOptions(): BullRootModuleOptions {
    return {
      connection: this.redisConfig.options,
      defaultJobOptions: {
        removeOnComplete: 1000,
        removeOnFail: 5000,
        attempts: 3,
      },
    };
  }

  createSharedConfiguration(): BullRootModuleOptions {
    return {
      connection: this.redisConfig.options,
      defaultJobOptions: {
        removeOnComplete: 1000,
        removeOnFail: 5000,
        attempts: 3,
      },
    };
  }
}
